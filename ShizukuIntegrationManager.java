package com.my.ffh4xinjector.system;

import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Build;
import android.util.Log;
import rikka.shizuku.Shizuku;
import rikka.shizuku.ShizukuBinderWrapper;
import rikka.shizuku.SystemServiceHelper;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.List;

/**
 * <PERSON>zuku Integration Manager for FFH4X
 * Provides WiFi debugging and system-level access through Shi<PERSON>ku
 */
public class ShizukuIntegrationManager {
    private static final String TAG = "ShizukuIntegration";
    private static final int SHIZUKU_REQUEST_CODE = 1001;
    
    // Shizuku package name
    private static final String SHIZUKU_PACKAGE = "moe.shizuku.privileged.api";
    
    private Context context;
    private static ShizukuIntegrationManager instance;
    private boolean isShizukuAvailable = false;
    private boolean isPermissionGranted = false;
    
    // Listeners
    private OnShizukuStatusListener statusListener;
    private OnWifiDebugListener wifiDebugListener;
    
    public interface OnShizukuStatusListener {
        void onShizukuAvailable();
        void onShizukuUnavailable(String reason);
        void onPermissionGranted();
        void onPermissionDenied();
    }
    
    public interface OnWifiDebugListener {
        void onWifiDebugEnabled();
        void onWifiDebugDisabled();
        void onWifiDebugFailed(String reason);
        void onAdbPortChanged(int port);
    }
    
    private ShizukuIntegrationManager(Context context) {
        this.context = context.getApplicationContext();
        initializeShizuku();
    }
    
    public static synchronized ShizukuIntegrationManager getInstance(Context context) {
        if (instance == null) {
            instance = new ShizukuIntegrationManager(context);
        }
        return instance;
    }
    
    /**
     * Initialize Shizuku integration
     */
    private void initializeShizuku() {
        try {
            // Check if Shizuku is installed
            if (!isShizukuInstalled()) {
                Log.w(TAG, "Shizuku is not installed");
                if (statusListener != null) {
                    statusListener.onShizukuUnavailable("Shizuku app is not installed");
                }
                return;
            }
            
            // Add permission request listener
            Shizuku.addRequestPermissionResultListener(permissionResultListener);
            
            // Check if Shizuku service is running
            if (Shizuku.pingBinder()) {
                isShizukuAvailable = true;
                Log.d(TAG, "Shizuku service is available");
                
                // Check permission
                checkShizukuPermission();
                
                if (statusListener != null) {
                    statusListener.onShizukuAvailable();
                }
            } else {
                Log.w(TAG, "Shizuku service is not running");
                if (statusListener != null) {
                    statusListener.onShizukuUnavailable("Shizuku service is not running");
                }
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error initializing Shizuku", e);
            if (statusListener != null) {
                statusListener.onShizukuUnavailable("Initialization error: " + e.getMessage());
            }
        }
    }
    
    /**
     * Check if Shizuku app is installed
     */
    private boolean isShizukuInstalled() {
        try {
            context.getPackageManager().getPackageInfo(SHIZUKU_PACKAGE, 0);
            return true;
        } catch (PackageManager.NameNotFoundException e) {
            return false;
        }
    }
    
    /**
     * Check Shizuku permission status
     */
    private void checkShizukuPermission() {
        if (Shizuku.checkSelfPermission() == PackageManager.PERMISSION_GRANTED) {
            isPermissionGranted = true;
            Log.d(TAG, "Shizuku permission granted");
            if (statusListener != null) {
                statusListener.onPermissionGranted();
            }
        } else {
            isPermissionGranted = false;
            Log.d(TAG, "Shizuku permission not granted");
        }
    }
    
    /**
     * Request Shizuku permission
     */
    public void requestShizukuPermission() {
        if (!isShizukuAvailable) {
            Log.w(TAG, "Shizuku is not available");
            return;
        }
        
        if (isPermissionGranted) {
            Log.d(TAG, "Permission already granted");
            return;
        }
        
        if (Shizuku.shouldShowRequestPermissionRationale()) {
            Log.d(TAG, "Should show permission rationale");
        }
        
        Shizuku.requestPermission(SHIZUKU_REQUEST_CODE);
    }
    
    /**
     * Permission result listener
     */
    private final Shizuku.OnRequestPermissionResultListener permissionResultListener = 
        new Shizuku.OnRequestPermissionResultListener() {
            @Override
            public void onRequestPermissionResult(int requestCode, int grantResult) {
                if (requestCode == SHIZUKU_REQUEST_CODE) {
                    if (grantResult == PackageManager.PERMISSION_GRANTED) {
                        isPermissionGranted = true;
                        Log.d(TAG, "Shizuku permission granted");
                        if (statusListener != null) {
                            statusListener.onPermissionGranted();
                        }
                    } else {
                        isPermissionGranted = false;
                        Log.d(TAG, "Shizuku permission denied");
                        if (statusListener != null) {
                            statusListener.onPermissionDenied();
                        }
                    }
                }
            }
        };
    
    /**
     * Enable WiFi debugging through Shizuku
     */
    public void enableWifiDebugging() {
        enableWifiDebugging(5555); // Default ADB port
    }
    
    /**
     * Enable WiFi debugging with custom port
     */
    public void enableWifiDebugging(int port) {
        if (!isShizukuAvailable || !isPermissionGranted) {
            Log.w(TAG, "Shizuku not available or permission not granted");
            if (wifiDebugListener != null) {
                wifiDebugListener.onWifiDebugFailed("Shizuku permission required");
            }
            return;
        }
        
        try {
            Log.d(TAG, "Enabling WiFi debugging on port " + port);
            
            // Execute ADB commands through Shizuku
            executeShizukuCommand("setprop service.adb.tcp.port " + port, new OnCommandResultListener() {
                @Override
                public void onCommandSuccess(String result) {
                    // Restart ADB to apply changes
                    executeShizukuCommand("stop adbd", new OnCommandResultListener() {
                        @Override
                        public void onCommandSuccess(String result) {
                            executeShizukuCommand("start adbd", new OnCommandResultListener() {
                                @Override
                                public void onCommandSuccess(String result) {
                                    Log.d(TAG, "WiFi debugging enabled successfully");
                                    if (wifiDebugListener != null) {
                                        wifiDebugListener.onWifiDebugEnabled();
                                        wifiDebugListener.onAdbPortChanged(port);
                                    }
                                }
                                
                                @Override
                                public void onCommandFailed(String error) {
                                    Log.e(TAG, "Failed to start adbd: " + error);
                                    if (wifiDebugListener != null) {
                                        wifiDebugListener.onWifiDebugFailed("Failed to start ADB daemon");
                                    }
                                }
                            });
                        }
                        
                        @Override
                        public void onCommandFailed(String error) {
                            Log.e(TAG, "Failed to stop adbd: " + error);
                            if (wifiDebugListener != null) {
                                wifiDebugListener.onWifiDebugFailed("Failed to restart ADB daemon");
                            }
                        }
                    });
                }
                
                @Override
                public void onCommandFailed(String error) {
                    Log.e(TAG, "Failed to set ADB port: " + error);
                    if (wifiDebugListener != null) {
                        wifiDebugListener.onWifiDebugFailed("Failed to set ADB port");
                    }
                }
            });
            
        } catch (Exception e) {
            Log.e(TAG, "Error enabling WiFi debugging", e);
            if (wifiDebugListener != null) {
                wifiDebugListener.onWifiDebugFailed("Error: " + e.getMessage());
            }
        }
    }
    
    /**
     * Disable WiFi debugging
     */
    public void disableWifiDebugging() {
        if (!isShizukuAvailable || !isPermissionGranted) {
            Log.w(TAG, "Shizuku not available or permission not granted");
            return;
        }
        
        try {
            Log.d(TAG, "Disabling WiFi debugging");
            
            executeShizukuCommand("setprop service.adb.tcp.port -1", new OnCommandResultListener() {
                @Override
                public void onCommandSuccess(String result) {
                    executeShizukuCommand("stop adbd", new OnCommandResultListener() {
                        @Override
                        public void onCommandSuccess(String result) {
                            executeShizukuCommand("start adbd", new OnCommandResultListener() {
                                @Override
                                public void onCommandSuccess(String result) {
                                    Log.d(TAG, "WiFi debugging disabled successfully");
                                    if (wifiDebugListener != null) {
                                        wifiDebugListener.onWifiDebugDisabled();
                                    }
                                }
                                
                                @Override
                                public void onCommandFailed(String error) {
                                    Log.e(TAG, "Failed to restart adbd: " + error);
                                }
                            });
                        }
                        
                        @Override
                        public void onCommandFailed(String error) {
                            Log.e(TAG, "Failed to stop adbd: " + error);
                        }
                    });
                }
                
                @Override
                public void onCommandFailed(String error) {
                    Log.e(TAG, "Failed to disable ADB port: " + error);
                }
            });
            
        } catch (Exception e) {
            Log.e(TAG, "Error disabling WiFi debugging", e);
        }
    }
    
    /**
     * Execute shell command through Shizuku
     */
    public void executeShizukuCommand(String command, OnCommandResultListener listener) {
        if (!isShizukuAvailable || !isPermissionGranted) {
            listener.onCommandFailed("Shizuku not available or permission not granted");
            return;
        }
        
        new Thread(() -> {
            try {
                Process process = Shizuku.newProcess(new String[]{"sh", "-c", command}, null, null);
                
                BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
                
                StringBuilder output = new StringBuilder();
                StringBuilder error = new StringBuilder();
                
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
                
                while ((line = errorReader.readLine()) != null) {
                    error.append(line).append("\n");
                }
                
                int exitCode = process.waitFor();
                
                if (exitCode == 0) {
                    listener.onCommandSuccess(output.toString());
                } else {
                    listener.onCommandFailed("Exit code: " + exitCode + ", Error: " + error.toString());
                }
                
            } catch (Exception e) {
                listener.onCommandFailed("Exception: " + e.getMessage());
            }
        }).start();
    }
    
    /**
     * Get current ADB port
     */
    public void getCurrentAdbPort(OnAdbPortListener listener) {
        executeShizukuCommand("getprop service.adb.tcp.port", new OnCommandResultListener() {
            @Override
            public void onCommandSuccess(String result) {
                try {
                    String portStr = result.trim();
                    if (portStr.isEmpty() || portStr.equals("-1")) {
                        listener.onPortReceived(-1); // WiFi debugging disabled
                    } else {
                        int port = Integer.parseInt(portStr);
                        listener.onPortReceived(port);
                    }
                } catch (NumberFormatException e) {
                    listener.onPortFailed("Invalid port format: " + result);
                }
            }
            
            @Override
            public void onCommandFailed(String error) {
                listener.onPortFailed(error);
            }
        });
    }
    
    /**
     * Check if WiFi debugging is enabled
     */
    public void isWifiDebuggingEnabled(OnWifiDebugStatusListener listener) {
        getCurrentAdbPort(new OnAdbPortListener() {
            @Override
            public void onPortReceived(int port) {
                listener.onStatusReceived(port > 0);
            }
            
            @Override
            public void onPortFailed(String error) {
                listener.onStatusFailed(error);
            }
        });
    }
    
    /**
     * Get device compatibility info
     */
    public DeviceCompatibilityInfo getDeviceCompatibility() {
        DeviceCompatibilityInfo info = new DeviceCompatibilityInfo();
        
        info.androidVersion = Build.VERSION.RELEASE;
        info.apiLevel = Build.VERSION.SDK_INT;
        info.manufacturer = Build.MANUFACTURER;
        info.model = Build.MODEL;
        info.isShizukuInstalled = isShizukuInstalled();
        info.isShizukuAvailable = isShizukuAvailable;
        info.hasShizukuPermission = isPermissionGranted;
        info.isRooted = checkRootAccess();
        info.supportedFeatures = getSupportedFeatures();
        
        return info;
    }
    
    /**
     * Check if device has root access
     */
    private boolean checkRootAccess() {
        try {
            Process process = Runtime.getRuntime().exec("su -c 'echo test'");
            process.waitFor();
            return process.exitValue() == 0;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Get supported features based on device capabilities
     */
    private String[] getSupportedFeatures() {
        return new String[]{
            "WiFi Debugging",
            "System Commands",
            "Property Modification",
            "Service Control",
            "Package Management"
        };
    }
    
    // Cleanup
    public void cleanup() {
        if (isShizukuAvailable) {
            Shizuku.removeRequestPermissionResultListener(permissionResultListener);
        }
    }
    
    // Setters for listeners
    public void setOnShizukuStatusListener(OnShizukuStatusListener listener) {
        this.statusListener = listener;
    }
    
    public void setOnWifiDebugListener(OnWifiDebugListener listener) {
        this.wifiDebugListener = listener;
    }
    
    // Getters
    public boolean isShizukuAvailable() {
        return isShizukuAvailable;
    }
    
    public boolean hasShizukuPermission() {
        return isPermissionGranted;
    }
    
    // Inner classes and interfaces
    public interface OnCommandResultListener {
        void onCommandSuccess(String result);
        void onCommandFailed(String error);
    }
    
    public interface OnAdbPortListener {
        void onPortReceived(int port);
        void onPortFailed(String error);
    }
    
    public interface OnWifiDebugStatusListener {
        void onStatusReceived(boolean enabled);
        void onStatusFailed(String error);
    }
    
    public static class DeviceCompatibilityInfo {
        public String androidVersion;
        public int apiLevel;
        public String manufacturer;
        public String model;
        public boolean isShizukuInstalled;
        public boolean isShizukuAvailable;
        public boolean hasShizukuPermission;
        public boolean isRooted;
        public String[] supportedFeatures;
        
        @Override
        public String toString() {
            return "Device: " + manufacturer + " " + model + "\n" +
                   "Android: " + androidVersion + " (API " + apiLevel + ")\n" +
                   "Shizuku: " + (isShizukuInstalled ? "Installed" : "Not Installed") + "\n" +
                   "Root: " + (isRooted ? "Yes" : "No");
        }
    }
}
