package com.my.ffh4xinjector.ui;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.*;
import androidx.cardview.widget.CardView;
import com.my.ffh4xinjector.system.ShizukuIntegrationManager;
import com.my.ffh4xinjector.compatibility.UniversalCompatibilityManager;
import com.my.ffh4xinjector.R;

/**
 * WiFi Debug Controller
 * Manages WiFi debugging functionality through Shi<PERSON>ku integration
 */
public class WiFiDebugController {
    private static final String TAG = "WiFiDebugController";
    
    private Context context;
    private View panelView;
    private ShizukuIntegrationManager shizukuManager;
    private UniversalCompatibilityManager compatibilityManager;
    
    // UI Components
    private TextView debugStatusText;
    private ImageView shizukuInstalledIcon;
    private ImageView shizukuRunningIcon;
    private ImageView shizukuPermissionIcon;
    private TextView shizukuInstalledText;
    private TextView shizukuRunningText;
    private TextView shizukuPermissionText;
    private Button requestPermissionButton;
    private TextView wifiDebugStatusText;
    private EditText adbPortEdit;
    private Button enableWifiDebugButton;
    private Button disableWifiDebugButton;
    private TextView deviceInfoText;
    private TextView compatibilityLevelText;
    
    // Status tracking
    private boolean isWifiDebugEnabled = false;
    private int currentAdbPort = -1;
    
    // Listeners
    private OnWifiDebugChangeListener wifiDebugChangeListener;
    
    public interface OnWifiDebugChangeListener {
        void onWifiDebugEnabled(int port);
        void onWifiDebugDisabled();
        void onShizukuStatusChanged(boolean available);
    }
    
    public WiFiDebugController(Context context) {
        this.context = context;
        this.shizukuManager = ShizukuIntegrationManager.getInstance(context);
        this.compatibilityManager = UniversalCompatibilityManager.getInstance(context);
        initializePanel();
        setupShizukuListeners();
        loadDeviceCompatibility();
    }
    
    private void initializePanel() {
        LayoutInflater inflater = LayoutInflater.from(context);
        panelView = inflater.inflate(R.layout.wifi_debug_panel, null);
        
        findViews();
        setupListeners();
        updateUI();
        checkCurrentStatus();
    }
    
    private void findViews() {
        debugStatusText = panelView.findViewById(R.id.text_debug_status);
        shizukuInstalledIcon = panelView.findViewById(R.id.icon_shizuku_installed);
        shizukuRunningIcon = panelView.findViewById(R.id.icon_shizuku_running);
        shizukuPermissionIcon = panelView.findViewById(R.id.icon_shizuku_permission);
        shizukuInstalledText = panelView.findViewById(R.id.text_shizuku_installed);
        shizukuRunningText = panelView.findViewById(R.id.text_shizuku_running);
        shizukuPermissionText = panelView.findViewById(R.id.text_shizuku_permission);
        requestPermissionButton = panelView.findViewById(R.id.btn_request_permission);
        wifiDebugStatusText = panelView.findViewById(R.id.text_wifi_debug_status);
        adbPortEdit = panelView.findViewById(R.id.edit_adb_port);
        enableWifiDebugButton = panelView.findViewById(R.id.btn_enable_wifi_debug);
        disableWifiDebugButton = panelView.findViewById(R.id.btn_disable_wifi_debug);
        deviceInfoText = panelView.findViewById(R.id.text_device_info);
        compatibilityLevelText = panelView.findViewById(R.id.text_compatibility_level);
    }
    
    private void setupListeners() {
        // ADB port input validation
        adbPortEdit.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}
            
            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {}
            
            @Override
            public void afterTextChanged(Editable s) {
                validateAdbPort();
            }
        });
        
        // Request permission button
        requestPermissionButton.setOnClickListener(v -> requestShizukuPermission());
        
        // Enable WiFi debug button
        enableWifiDebugButton.setOnClickListener(v -> enableWifiDebugging());
        
        // Disable WiFi debug button
        disableWifiDebugButton.setOnClickListener(v -> disableWifiDebugging());
    }
    
    private void setupShizukuListeners() {
        shizukuManager.setOnShizukuStatusListener(new ShizukuIntegrationManager.OnShizukuStatusListener() {
            @Override
            public void onShizukuAvailable() {
                updateShizukuStatus();
                if (wifiDebugChangeListener != null) {
                    wifiDebugChangeListener.onShizukuStatusChanged(true);
                }
            }
            
            @Override
            public void onShizukuUnavailable(String reason) {
                updateShizukuStatus();
                showMessage("Shizuku unavailable: " + reason, false);
                if (wifiDebugChangeListener != null) {
                    wifiDebugChangeListener.onShizukuStatusChanged(false);
                }
            }
            
            @Override
            public void onPermissionGranted() {
                updateShizukuStatus();
                showMessage("Shizuku permission granted", true);
            }
            
            @Override
            public void onPermissionDenied() {
                updateShizukuStatus();
                showMessage("Shizuku permission denied", false);
            }
        });
        
        shizukuManager.setOnWifiDebugListener(new ShizukuIntegrationManager.OnWifiDebugListener() {
            @Override
            public void onWifiDebugEnabled() {
                isWifiDebugEnabled = true;
                updateWifiDebugStatus();
                showMessage("WiFi debugging enabled successfully", true);
                if (wifiDebugChangeListener != null) {
                    wifiDebugChangeListener.onWifiDebugEnabled(currentAdbPort);
                }
            }
            
            @Override
            public void onWifiDebugDisabled() {
                isWifiDebugEnabled = false;
                currentAdbPort = -1;
                updateWifiDebugStatus();
                showMessage("WiFi debugging disabled", true);
                if (wifiDebugChangeListener != null) {
                    wifiDebugChangeListener.onWifiDebugDisabled();
                }
            }
            
            @Override
            public void onWifiDebugFailed(String reason) {
                showMessage("WiFi debug failed: " + reason, false);
            }
            
            @Override
            public void onAdbPortChanged(int port) {
                currentAdbPort = port;
                updateWifiDebugStatus();
            }
        });
    }
    
    private void loadDeviceCompatibility() {
        UniversalCompatibilityManager.DeviceInfo deviceInfo = compatibilityManager.getDeviceInfo();
        UniversalCompatibilityManager.CompatibilityProfile profile = compatibilityManager.getCompatibilityProfile();
        
        // Update device info display
        StringBuilder deviceInfoBuilder = new StringBuilder();
        deviceInfoBuilder.append("Device: ").append(deviceInfo.manufacturer).append(" ").append(deviceInfo.model).append("\n");
        deviceInfoBuilder.append("Android: ").append(deviceInfo.androidVersion).append(" (API ").append(deviceInfo.apiLevel).append(")\n");
        deviceInfoBuilder.append("Category: ").append(deviceInfo.category.toString()).append("\n");
        deviceInfoBuilder.append("Root: ").append(deviceInfo.hasRoot ? "Yes" : "No").append("\n");
        deviceInfoBuilder.append("Magisk: ").append(deviceInfo.hasMagisk ? "Yes" : "No");
        
        deviceInfoText.setText(deviceInfoBuilder.toString());
        
        // Update compatibility level
        updateCompatibilityLevel(profile.shizukuCompatibility);
        
        // Show optimization tips if needed
        String tips = compatibilityManager.getOptimizationTips();
        if (!tips.isEmpty()) {
            showMessage("Optimization tips:\n" + tips, true);
        }
    }
    
    private void updateCompatibilityLevel(UniversalCompatibilityManager.CompatibilityLevel level) {
        compatibilityLevelText.setText(level.toString());
        
        int color;
        switch (level) {
            case FULL:
                color = R.color.status_success;
                break;
            case PARTIAL:
                color = R.color.status_warning;
                break;
            case LIMITED:
                color = R.color.status_limited;
                break;
            case EXPERIMENTAL:
                color = R.color.status_experimental;
                break;
            default:
                color = R.color.status_error;
                break;
        }
        
        compatibilityLevelText.setTextColor(context.getColor(color));
    }
    
    private void updateUI() {
        updateShizukuStatus();
        updateWifiDebugStatus();
        updateButtonStates();
    }
    
    private void updateShizukuStatus() {
        // Check Shizuku installation
        boolean isInstalled = shizukuManager.isShizukuAvailable();
        updateStatusIndicator(shizukuInstalledIcon, shizukuInstalledText, isInstalled);
        
        // Check Shizuku service
        boolean isRunning = shizukuManager.isShizukuAvailable();
        updateStatusIndicator(shizukuRunningIcon, shizukuRunningText, isRunning);
        
        // Check Shizuku permission
        boolean hasPermission = shizukuManager.hasShizukuPermission();
        updateStatusIndicator(shizukuPermissionIcon, shizukuPermissionText, hasPermission);
        
        // Show/hide request permission button
        requestPermissionButton.setVisibility(
            isInstalled && isRunning && !hasPermission ? View.VISIBLE : View.GONE
        );
    }
    
    private void updateStatusIndicator(ImageView icon, TextView text, boolean status) {
        if (status) {
            icon.setImageResource(R.drawable.ic_check_circle);
            icon.setColorFilter(context.getColor(R.color.status_success));
            text.setText("YES");
            text.setTextColor(context.getColor(R.color.status_success));
        } else {
            icon.setImageResource(R.drawable.ic_cancel);
            icon.setColorFilter(context.getColor(R.color.status_error));
            text.setText("NO");
            text.setTextColor(context.getColor(R.color.status_error));
        }
    }
    
    private void updateWifiDebugStatus() {
        if (isWifiDebugEnabled && currentAdbPort > 0) {
            debugStatusText.setText("ENABLED");
            debugStatusText.setTextColor(context.getColor(R.color.status_success));
            
            wifiDebugStatusText.setText("Enabled on port " + currentAdbPort);
            wifiDebugStatusText.setTextColor(context.getColor(R.color.status_success));
        } else {
            debugStatusText.setText("DISABLED");
            debugStatusText.setTextColor(context.getColor(R.color.status_inactive));
            
            wifiDebugStatusText.setText("Disabled");
            wifiDebugStatusText.setTextColor(context.getColor(R.color.status_inactive));
        }
    }
    
    private void updateButtonStates() {
        boolean canUseShizuku = shizukuManager.isShizukuAvailable() && shizukuManager.hasShizukuPermission();
        
        enableWifiDebugButton.setEnabled(canUseShizuku && !isWifiDebugEnabled);
        disableWifiDebugButton.setEnabled(canUseShizuku && isWifiDebugEnabled);
        
        float alpha = canUseShizuku ? 1.0f : 0.5f;
        enableWifiDebugButton.setAlpha(alpha);
        disableWifiDebugButton.setAlpha(alpha);
    }
    
    private void validateAdbPort() {
        try {
            String portStr = adbPortEdit.getText().toString().trim();
            if (!portStr.isEmpty()) {
                int port = Integer.parseInt(portStr);
                if (port < 1024 || port > 65535) {
                    adbPortEdit.setError("Port must be between 1024-65535");
                } else {
                    adbPortEdit.setError(null);
                }
            }
        } catch (NumberFormatException e) {
            adbPortEdit.setError("Invalid port number");
        }
    }
    
    private void requestShizukuPermission() {
        if (!shizukuManager.isShizukuAvailable()) {
            showInstallShizukuDialog();
            return;
        }
        
        shizukuManager.requestShizukuPermission();
    }
    
    private void enableWifiDebugging() {
        try {
            String portStr = adbPortEdit.getText().toString().trim();
            int port = portStr.isEmpty() ? 5555 : Integer.parseInt(portStr);
            
            if (port < 1024 || port > 65535) {
                showMessage("Invalid port number. Use 1024-65535", false);
                return;
            }
            
            enableWifiDebugButton.setEnabled(false);
            enableWifiDebugButton.setText("Enabling...");
            
            shizukuManager.enableWifiDebugging(port);
            
        } catch (NumberFormatException e) {
            showMessage("Invalid port number", false);
        }
    }
    
    private void disableWifiDebugging() {
        disableWifiDebugButton.setEnabled(false);
        disableWifiDebugButton.setText("Disabling...");
        
        shizukuManager.disableWifiDebugging();
    }
    
    private void checkCurrentStatus() {
        if (shizukuManager.isShizukuAvailable() && shizukuManager.hasShizukuPermission()) {
            shizukuManager.isWifiDebuggingEnabled(new ShizukuIntegrationManager.OnWifiDebugStatusListener() {
                @Override
                public void onStatusReceived(boolean enabled) {
                    isWifiDebugEnabled = enabled;
                    if (enabled) {
                        shizukuManager.getCurrentAdbPort(new ShizukuIntegrationManager.OnAdbPortListener() {
                            @Override
                            public void onPortReceived(int port) {
                                currentAdbPort = port;
                                updateWifiDebugStatus();
                                updateButtonStates();
                            }
                            
                            @Override
                            public void onPortFailed(String error) {
                                // Handle error
                            }
                        });
                    } else {
                        updateWifiDebugStatus();
                        updateButtonStates();
                    }
                }
                
                @Override
                public void onStatusFailed(String error) {
                    // Handle error
                }
            });
        }
    }
    
    private void showInstallShizukuDialog() {
        // Create dialog to guide user to install Shizuku
        android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(context);
        builder.setTitle("Shizuku Required");
        builder.setMessage("Shizuku app is required for WiFi debugging. Would you like to download it?");
        builder.setPositiveButton("Download", (dialog, which) -> {
            Intent intent = new Intent(Intent.ACTION_VIEW);
            intent.setData(Uri.parse("https://github.com/RikkaApps/Shizuku/releases"));
            context.startActivity(intent);
        });
        builder.setNegativeButton("Cancel", null);
        builder.show();
    }
    
    private void showMessage(String message, boolean isSuccess) {
        Toast.makeText(context, message, Toast.LENGTH_LONG).show();
    }
    
    // Public methods
    public View getPanelView() {
        return panelView;
    }
    
    public void setOnWifiDebugChangeListener(OnWifiDebugChangeListener listener) {
        this.wifiDebugChangeListener = listener;
    }
    
    public void refreshPanel() {
        updateUI();
        checkCurrentStatus();
        loadDeviceCompatibility();
    }
    
    public boolean isWifiDebuggingEnabled() {
        return isWifiDebugEnabled;
    }
    
    public int getCurrentAdbPort() {
        return currentAdbPort;
    }
    
    public boolean isShizukuReady() {
        return shizukuManager.isShizukuAvailable() && shizukuManager.hasShizukuPermission();
    }
    
    public void cleanup() {
        shizukuManager.cleanup();
    }
}
