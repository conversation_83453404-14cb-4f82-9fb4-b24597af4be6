package com.my.ffh4xinjector.autoaim;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

/**
 * Enhanced AutoAim Configuration System
 * Provides centralized management of autoaim settings with validation and persistence
 */
public class AutoAimConfig {
    private static final String TAG = "AutoAimConfig";
    private static final String PREFS_NAME = "autoaim_settings";
    
    // Configuration Keys
    private static final String KEY_ENABLED = "autoaim_enabled";
    private static final String KEY_SENSITIVITY = "aim_sensitivity";
    private static final String KEY_RANGE = "aim_range";
    private static final String KEY_TARGET_PRIORITY = "target_priority";
    private static final String KEY_SMOOTHING = "aim_smoothing";
    private static final String KEY_HEADSHOT_PRIORITY = "headshot_priority";
    private static final String KEY_VISIBLE_ONLY = "visible_targets_only";
    private static final String KEY_AUTO_FIRE = "auto_fire";
    private static final String KEY_PREDICTION = "target_prediction";
    private static final String KEY_FOV_LIMIT = "fov_limit";
    
    // Default Values
    private static final boolean DEFAULT_ENABLED = false;
    private static final float DEFAULT_SENSITIVITY = 0.5f;
    private static final float DEFAULT_RANGE = 100.0f;
    private static final int DEFAULT_TARGET_PRIORITY = 0; // 0=Closest, 1=Lowest HP, 2=Headshot
    private static final boolean DEFAULT_SMOOTHING = true;
    private static final boolean DEFAULT_HEADSHOT_PRIORITY = false;
    private static final boolean DEFAULT_VISIBLE_ONLY = true;
    private static final boolean DEFAULT_AUTO_FIRE = false;
    private static final boolean DEFAULT_PREDICTION = false;
    private static final float DEFAULT_FOV_LIMIT = 90.0f;
    
    // Validation Limits
    private static final float MIN_SENSITIVITY = 0.1f;
    private static final float MAX_SENSITIVITY = 2.0f;
    private static final float MIN_RANGE = 10.0f;
    private static final float MAX_RANGE = 500.0f;
    private static final float MIN_FOV = 30.0f;
    private static final float MAX_FOV = 180.0f;
    
    private SharedPreferences prefs;
    private static AutoAimConfig instance;
    
    private AutoAimConfig(Context context) {
        prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
    }
    
    public static synchronized AutoAimConfig getInstance(Context context) {
        if (instance == null) {
            instance = new AutoAimConfig(context.getApplicationContext());
        }
        return instance;
    }
    
    // Getters with validation
    public boolean isEnabled() {
        return prefs.getBoolean(KEY_ENABLED, DEFAULT_ENABLED);
    }
    
    public float getSensitivity() {
        float value = prefs.getFloat(KEY_SENSITIVITY, DEFAULT_SENSITIVITY);
        return Math.max(MIN_SENSITIVITY, Math.min(MAX_SENSITIVITY, value));
    }
    
    public float getRange() {
        float value = prefs.getFloat(KEY_RANGE, DEFAULT_RANGE);
        return Math.max(MIN_RANGE, Math.min(MAX_RANGE, value));
    }
    
    public int getTargetPriority() {
        return prefs.getInt(KEY_TARGET_PRIORITY, DEFAULT_TARGET_PRIORITY);
    }
    
    public boolean isSmoothingEnabled() {
        return prefs.getBoolean(KEY_SMOOTHING, DEFAULT_SMOOTHING);
    }
    
    public boolean isHeadshotPriority() {
        return prefs.getBoolean(KEY_HEADSHOT_PRIORITY, DEFAULT_HEADSHOT_PRIORITY);
    }
    
    public boolean isVisibleTargetsOnly() {
        return prefs.getBoolean(KEY_VISIBLE_ONLY, DEFAULT_VISIBLE_ONLY);
    }
    
    public boolean isAutoFireEnabled() {
        return prefs.getBoolean(KEY_AUTO_FIRE, DEFAULT_AUTO_FIRE);
    }
    
    public boolean isPredictionEnabled() {
        return prefs.getBoolean(KEY_PREDICTION, DEFAULT_PREDICTION);
    }
    
    public float getFovLimit() {
        float value = prefs.getFloat(KEY_FOV_LIMIT, DEFAULT_FOV_LIMIT);
        return Math.max(MIN_FOV, Math.min(MAX_FOV, value));
    }
    
    // Setters with validation
    public void setEnabled(boolean enabled) {
        prefs.edit().putBoolean(KEY_ENABLED, enabled).apply();
        Log.d(TAG, "AutoAim enabled: " + enabled);
    }
    
    public void setSensitivity(float sensitivity) {
        float validValue = Math.max(MIN_SENSITIVITY, Math.min(MAX_SENSITIVITY, sensitivity));
        prefs.edit().putFloat(KEY_SENSITIVITY, validValue).apply();
        Log.d(TAG, "Sensitivity set to: " + validValue);
    }
    
    public void setRange(float range) {
        float validValue = Math.max(MIN_RANGE, Math.min(MAX_RANGE, range));
        prefs.edit().putFloat(KEY_RANGE, validValue).apply();
        Log.d(TAG, "Range set to: " + validValue);
    }
    
    public void setTargetPriority(int priority) {
        int validValue = Math.max(0, Math.min(2, priority));
        prefs.edit().putInt(KEY_TARGET_PRIORITY, validValue).apply();
        Log.d(TAG, "Target priority set to: " + validValue);
    }
    
    public void setSmoothingEnabled(boolean enabled) {
        prefs.edit().putBoolean(KEY_SMOOTHING, enabled).apply();
        Log.d(TAG, "Smoothing enabled: " + enabled);
    }
    
    public void setHeadshotPriority(boolean enabled) {
        prefs.edit().putBoolean(KEY_HEADSHOT_PRIORITY, enabled).apply();
        Log.d(TAG, "Headshot priority enabled: " + enabled);
    }
    
    public void setVisibleTargetsOnly(boolean enabled) {
        prefs.edit().putBoolean(KEY_VISIBLE_ONLY, enabled).apply();
        Log.d(TAG, "Visible targets only: " + enabled);
    }
    
    public void setAutoFireEnabled(boolean enabled) {
        prefs.edit().putBoolean(KEY_AUTO_FIRE, enabled).apply();
        Log.d(TAG, "Auto fire enabled: " + enabled);
    }
    
    public void setPredictionEnabled(boolean enabled) {
        prefs.edit().putBoolean(KEY_PREDICTION, enabled).apply();
        Log.d(TAG, "Prediction enabled: " + enabled);
    }
    
    public void setFovLimit(float fov) {
        float validValue = Math.max(MIN_FOV, Math.min(MAX_FOV, fov));
        prefs.edit().putFloat(KEY_FOV_LIMIT, validValue).apply();
        Log.d(TAG, "FOV limit set to: " + validValue);
    }
    
    // Utility methods
    public void resetToDefaults() {
        SharedPreferences.Editor editor = prefs.edit();
        editor.putBoolean(KEY_ENABLED, DEFAULT_ENABLED);
        editor.putFloat(KEY_SENSITIVITY, DEFAULT_SENSITIVITY);
        editor.putFloat(KEY_RANGE, DEFAULT_RANGE);
        editor.putInt(KEY_TARGET_PRIORITY, DEFAULT_TARGET_PRIORITY);
        editor.putBoolean(KEY_SMOOTHING, DEFAULT_SMOOTHING);
        editor.putBoolean(KEY_HEADSHOT_PRIORITY, DEFAULT_HEADSHOT_PRIORITY);
        editor.putBoolean(KEY_VISIBLE_ONLY, DEFAULT_VISIBLE_ONLY);
        editor.putBoolean(KEY_AUTO_FIRE, DEFAULT_AUTO_FIRE);
        editor.putBoolean(KEY_PREDICTION, DEFAULT_PREDICTION);
        editor.putFloat(KEY_FOV_LIMIT, DEFAULT_FOV_LIMIT);
        editor.apply();
        Log.d(TAG, "Settings reset to defaults");
    }
    
    public String exportSettings() {
        StringBuilder sb = new StringBuilder();
        sb.append("AutoAim Settings Export:\n");
        sb.append("Enabled: ").append(isEnabled()).append("\n");
        sb.append("Sensitivity: ").append(getSensitivity()).append("\n");
        sb.append("Range: ").append(getRange()).append("\n");
        sb.append("Target Priority: ").append(getTargetPriority()).append("\n");
        sb.append("Smoothing: ").append(isSmoothingEnabled()).append("\n");
        sb.append("Headshot Priority: ").append(isHeadshotPriority()).append("\n");
        sb.append("Visible Only: ").append(isVisibleTargetsOnly()).append("\n");
        sb.append("Auto Fire: ").append(isAutoFireEnabled()).append("\n");
        sb.append("Prediction: ").append(isPredictionEnabled()).append("\n");
        sb.append("FOV Limit: ").append(getFovLimit()).append("\n");
        return sb.toString();
    }
    
    public boolean isValidConfiguration() {
        return getSensitivity() >= MIN_SENSITIVITY && getSensitivity() <= MAX_SENSITIVITY &&
               getRange() >= MIN_RANGE && getRange() <= MAX_RANGE &&
               getFovLimit() >= MIN_FOV && getFovLimit() <= MAX_FOV &&
               getTargetPriority() >= 0 && getTargetPriority() <= 2;
    }
}
