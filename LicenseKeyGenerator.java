package com.my.ffh4xinjector.admin;

import android.util.Log;
import com.google.firebase.database.*;
import java.security.MessageDigest;
import java.security.SecureRandom;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * License Key Generator for Admin Use
 * Generates and manages lifetime license keys for FFH4X
 */
public class LicenseKeyGenerator {
    private static final String TAG = "LicenseKeyGenerator";
    private static final String FIREBASE_LICENSES_PATH = "license_keys";
    
    // Character sets for key generation
    private static final String UPPERCASE_LETTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private static final String NUMBERS = "**********";
    private static final String KEY_CHARS = UPPERCASE_LETTERS + NUMBERS;
    
    private DatabaseReference firebaseDb;
    private SecureRandom secureRandom;
    
    public LicenseKeyGenerator() {
        this.firebaseDb = FirebaseDatabase.getInstance().getReference();
        this.secureRandom = new SecureRandom();
    }
    
    /**
     * Generate a new lifetime license key
     */
    public void generateLifetimeLicense(String createdBy, OnLicenseGeneratedListener listener) {
        generateLifetimeLicense(createdBy, 3, listener); // Default 3 devices
    }
    
    /**
     * Generate a new lifetime license key with custom device limit
     */
    public void generateLifetimeLicense(String createdBy, int maxDevices, OnLicenseGeneratedListener listener) {
        String licenseKey = generateLicenseKey();
        
        // Check if key already exists (very unlikely but possible)
        checkKeyExists(licenseKey, exists -> {
            if (exists) {
                // Generate a new key if collision detected
                generateLifetimeLicense(createdBy, maxDevices, listener);
            } else {
                // Create license data
                LicenseData licenseData = new LicenseData();
                licenseData.isValid = true;
                licenseData.isBanned = false;
                licenseData.creationDate = System.currentTimeMillis();
                licenseData.createdBy = createdBy;
                licenseData.maxDevices = maxDevices;
                licenseData.licenseType = "LIFETIME";
                licenseData.checksum = generateChecksum(licenseKey);
                
                // Save to Firebase
                saveLicenseToFirebase(licenseKey, licenseData, listener);
            }
        });
    }
    
    /**
     * Generate license key in format XXXX-XXXX-XXXX-XXXX
     */
    private String generateLicenseKey() {
        StringBuilder key = new StringBuilder();
        
        for (int segment = 0; segment < 4; segment++) {
            if (segment > 0) {
                key.append("-");
            }
            
            for (int i = 0; i < 4; i++) {
                int randomIndex = secureRandom.nextInt(KEY_CHARS.length());
                key.append(KEY_CHARS.charAt(randomIndex));
            }
        }
        
        return key.toString();
    }
    
    /**
     * Generate checksum for license validation
     */
    private String generateChecksum(String licenseKey) {
        try {
            String data = licenseKey + "FFH4X_SALT_2024";
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(data.getBytes());
            
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }
            
            return hexString.toString().substring(0, 16).toUpperCase();
        } catch (Exception e) {
            Log.e(TAG, "Error generating checksum", e);
            return "DEFAULT_CHECKSUM";
        }
    }
    
    /**
     * Check if license key already exists
     */
    private void checkKeyExists(String licenseKey, OnKeyExistsListener listener) {
        firebaseDb.child(FIREBASE_LICENSES_PATH).child(licenseKey)
            .addListenerForSingleValueEvent(new ValueEventListener() {
                @Override
                public void onDataChange(DataSnapshot snapshot) {
                    listener.onResult(snapshot.exists());
                }
                
                @Override
                public void onCancelled(DatabaseError error) {
                    Log.e(TAG, "Error checking key existence", error.toException());
                    listener.onResult(false); // Assume doesn't exist on error
                }
            });
    }
    
    /**
     * Save license to Firebase
     */
    private void saveLicenseToFirebase(String licenseKey, LicenseData licenseData, OnLicenseGeneratedListener listener) {
        firebaseDb.child(FIREBASE_LICENSES_PATH).child(licenseKey).setValue(licenseData)
            .addOnSuccessListener(aVoid -> {
                Log.d(TAG, "License key generated successfully: " + maskLicenseKey(licenseKey));
                listener.onLicenseGenerated(licenseKey, licenseData);
            })
            .addOnFailureListener(e -> {
                Log.e(TAG, "Failed to save license key", e);
                listener.onGenerationFailed("Failed to save license: " + e.getMessage());
            });
    }
    
    /**
     * Generate multiple license keys
     */
    public void generateBatchLicenses(String createdBy, int count, int maxDevices, OnBatchGeneratedListener listener) {
        BatchGenerationTask task = new BatchGenerationTask(createdBy, count, maxDevices, listener);
        task.start();
    }
    
    /**
     * Validate license key format
     */
    public static boolean isValidLicenseFormat(String licenseKey) {
        return licenseKey != null && licenseKey.matches("^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$");
    }
    
    /**
     * Ban a license key
     */
    public void banLicense(String licenseKey, String reason, OnLicenseActionListener listener) {
        if (!isValidLicenseFormat(licenseKey)) {
            listener.onActionFailed("Invalid license key format");
            return;
        }
        
        Map<String, Object> updates = new HashMap<>();
        updates.put("isBanned", true);
        updates.put("banReason", reason);
        updates.put("banDate", System.currentTimeMillis());
        
        firebaseDb.child(FIREBASE_LICENSES_PATH).child(licenseKey).updateChildren(updates)
            .addOnSuccessListener(aVoid -> {
                Log.d(TAG, "License banned: " + maskLicenseKey(licenseKey));
                listener.onActionSuccess("License banned successfully");
            })
            .addOnFailureListener(e -> {
                Log.e(TAG, "Failed to ban license", e);
                listener.onActionFailed("Failed to ban license: " + e.getMessage());
            });
    }
    
    /**
     * Unban a license key
     */
    public void unbanLicense(String licenseKey, OnLicenseActionListener listener) {
        if (!isValidLicenseFormat(licenseKey)) {
            listener.onActionFailed("Invalid license key format");
            return;
        }
        
        Map<String, Object> updates = new HashMap<>();
        updates.put("isBanned", false);
        updates.put("unbanDate", System.currentTimeMillis());
        
        firebaseDb.child(FIREBASE_LICENSES_PATH).child(licenseKey).updateChildren(updates)
            .addOnSuccessListener(aVoid -> {
                Log.d(TAG, "License unbanned: " + maskLicenseKey(licenseKey));
                listener.onActionSuccess("License unbanned successfully");
            })
            .addOnFailureListener(e -> {
                Log.e(TAG, "Failed to unban license", e);
                listener.onActionFailed("Failed to unban license: " + e.getMessage());
            });
    }
    
    /**
     * Get license information
     */
    public void getLicenseInfo(String licenseKey, OnLicenseInfoListener listener) {
        if (!isValidLicenseFormat(licenseKey)) {
            listener.onInfoFailed("Invalid license key format");
            return;
        }
        
        firebaseDb.child(FIREBASE_LICENSES_PATH).child(licenseKey)
            .addListenerForSingleValueEvent(new ValueEventListener() {
                @Override
                public void onDataChange(DataSnapshot snapshot) {
                    if (snapshot.exists()) {
                        LicenseData licenseData = snapshot.getValue(LicenseData.class);
                        if (licenseData != null) {
                            listener.onInfoReceived(licenseKey, licenseData);
                        } else {
                            listener.onInfoFailed("Invalid license data");
                        }
                    } else {
                        listener.onInfoFailed("License key not found");
                    }
                }
                
                @Override
                public void onCancelled(DatabaseError error) {
                    listener.onInfoFailed("Database error: " + error.getMessage());
                }
            });
    }
    
    /**
     * Mask license key for logging
     */
    private String maskLicenseKey(String licenseKey) {
        if (licenseKey == null || licenseKey.length() < 8) return "****";
        return licenseKey.substring(0, 4) + "-****-****-" + licenseKey.substring(licenseKey.length() - 4);
    }
    
    // Inner classes
    public static class LicenseData {
        public boolean isValid;
        public boolean isBanned;
        public long creationDate;
        public String createdBy;
        public int maxDevices;
        public String licenseType;
        public String checksum;
        public String banReason;
        public long banDate;
        public long unbanDate;
        
        public LicenseData() {} // Required for Firebase
    }
    
    private class BatchGenerationTask {
        private String createdBy;
        private int totalCount;
        private int maxDevices;
        private OnBatchGeneratedListener listener;
        private int completedCount = 0;
        private int failedCount = 0;
        private StringBuilder results = new StringBuilder();
        
        public BatchGenerationTask(String createdBy, int count, int maxDevices, OnBatchGeneratedListener listener) {
            this.createdBy = createdBy;
            this.totalCount = count;
            this.maxDevices = maxDevices;
            this.listener = listener;
        }
        
        public void start() {
            for (int i = 0; i < totalCount; i++) {
                generateLifetimeLicense(createdBy, maxDevices, new OnLicenseGeneratedListener() {
                    @Override
                    public void onLicenseGenerated(String licenseKey, LicenseData licenseData) {
                        completedCount++;
                        results.append(licenseKey).append("\n");
                        checkCompletion();
                    }
                    
                    @Override
                    public void onGenerationFailed(String reason) {
                        failedCount++;
                        checkCompletion();
                    }
                });
            }
        }
        
        private void checkCompletion() {
            if (completedCount + failedCount >= totalCount) {
                listener.onBatchCompleted(completedCount, failedCount, results.toString());
            }
        }
    }
    
    // Interfaces
    public interface OnLicenseGeneratedListener {
        void onLicenseGenerated(String licenseKey, LicenseData licenseData);
        void onGenerationFailed(String reason);
    }
    
    public interface OnBatchGeneratedListener {
        void onBatchCompleted(int successCount, int failedCount, String licenseKeys);
    }
    
    public interface OnLicenseActionListener {
        void onActionSuccess(String message);
        void onActionFailed(String reason);
    }
    
    public interface OnLicenseInfoListener {
        void onInfoReceived(String licenseKey, LicenseData licenseData);
        void onInfoFailed(String reason);
    }
    
    private interface OnKeyExistsListener {
        void onResult(boolean exists);
    }
}
