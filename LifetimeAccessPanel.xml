<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@drawable/panel_background"
    android:elevation="8dp">

    <!-- Header Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingBottom="16dp">

        <ImageView
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/ic_vip_crown"
            android:tint="@color/gold_accent" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Lifetime Access"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:layout_marginStart="12dp" />

        <TextView
            android:id="@+id/text_access_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="INACTIVE"
            android:textSize="12sp"
            android:textStyle="bold"
            android:textColor="@color/status_inactive"
            android:background="@drawable/status_badge_background"
            android:padding="6dp" />

    </LinearLayout>

    <!-- Status Card -->
    <androidx.cardview.widget.CardView
        android:id="@+id/card_status"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="6dp"
        app:cardBackgroundColor="@color/card_background">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <TextView
                android:id="@+id/text_status_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Access Status"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:layout_marginBottom="8dp" />

            <TextView
                android:id="@+id/text_status_description"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="No lifetime access detected. Please enter your license key to activate."
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:layout_marginBottom="12dp" />

            <!-- Device Info -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="8dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Device ID:"
                    android:textSize="12sp"
                    android:textColor="@color/text_secondary" />

                <TextView
                    android:id="@+id/text_device_id"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="XXXX-XXXX"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:fontFamily="monospace" />

            </LinearLayout>

            <!-- Activation Date -->
            <LinearLayout
                android:id="@+id/layout_activation_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:visibility="gone">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Activated:"
                    android:textSize="12sp"
                    android:textColor="@color/text_secondary" />

                <TextView
                    android:id="@+id/text_activation_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Never"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary" />

            </LinearLayout>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- License Key Input Card -->
    <androidx.cardview.widget.CardView
        android:id="@+id/card_license_input"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:cardBackgroundColor="@color/card_background">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="License Key Activation"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:layout_marginBottom="12dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Enter your lifetime license key to unlock all premium features"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:layout_marginBottom="16dp" />

            <!-- License Key Input -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:boxStrokeColor="@color/accent_color"
                app:hintTextColor="@color/accent_color">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edit_license_key"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="XXXX-XXXX-XXXX-XXXX"
                    android:inputType="textCapCharacters"
                    android:maxLength="19"
                    android:fontFamily="monospace"
                    android:textSize="16sp"
                    android:textAllCaps="true" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Activation Button -->
            <Button
                android:id="@+id/btn_activate"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="Activate Lifetime Access"
                android:textColor="@color/white"
                android:textStyle="bold"
                android:background="@drawable/button_primary_gradient"
                android:elevation="4dp" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- Features Card -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:cardBackgroundColor="@color/card_background">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Lifetime Features"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:layout_marginBottom="12dp" />

            <!-- Feature List -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- AutoAim Feature -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="8dp">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/ic_check_circle"
                        android:tint="@color/success_green" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Advanced AutoAim System"
                        android:textSize="14sp"
                        android:textColor="@color/text_primary"
                        android:layout_marginStart="12dp" />

                    <TextView
                        android:id="@+id/text_autoaim_status"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="LOCKED"
                        android:textSize="10sp"
                        android:textStyle="bold"
                        android:textColor="@color/status_locked"
                        android:background="@drawable/feature_status_background"
                        android:padding="4dp" />

                </LinearLayout>

                <!-- ESP Feature -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="8dp">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/ic_check_circle"
                        android:tint="@color/success_green" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="ESP & Wallhack"
                        android:textSize="14sp"
                        android:textColor="@color/text_primary"
                        android:layout_marginStart="12dp" />

                    <TextView
                        android:id="@+id/text_esp_status"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="LOCKED"
                        android:textSize="10sp"
                        android:textStyle="bold"
                        android:textColor="@color/status_locked"
                        android:background="@drawable/feature_status_background"
                        android:padding="4dp" />

                </LinearLayout>

                <!-- No Recoil Feature -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="8dp">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/ic_check_circle"
                        android:tint="@color/success_green" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="No Recoil & Spread"
                        android:textSize="14sp"
                        android:textColor="@color/text_primary"
                        android:layout_marginStart="12dp" />

                    <TextView
                        android:id="@+id/text_recoil_status"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="LOCKED"
                        android:textSize="10sp"
                        android:textStyle="bold"
                        android:textColor="@color/status_locked"
                        android:background="@drawable/feature_status_background"
                        android:padding="4dp" />

                </LinearLayout>

                <!-- Speed Hack Feature -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/ic_check_circle"
                        android:tint="@color/success_green" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Speed & Jump Hack"
                        android:textSize="14sp"
                        android:textColor="@color/text_primary"
                        android:layout_marginStart="12dp" />

                    <TextView
                        android:id="@+id/text_speed_status"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="LOCKED"
                        android:textSize="10sp"
                        android:textStyle="bold"
                        android:textColor="@color/status_locked"
                        android:background="@drawable/feature_status_background"
                        android:padding="4dp" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- Action Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center">

        <Button
            android:id="@+id/btn_check_status"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:text="Check Status"
            android:textColor="@color/text_secondary"
            android:background="@drawable/button_secondary"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/btn_contact_support"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:text="Support"
            android:textColor="@color/accent_color"
            android:background="@drawable/button_outline"
            android:layout_marginStart="8dp" />

    </LinearLayout>

</LinearLayout>
