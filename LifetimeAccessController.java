package com.my.ffh4xinjector.ui;

import android.content.Context;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.*;
import androidx.cardview.widget.CardView;
import com.google.android.material.textfield.TextInputEditText;
import com.my.ffh4xinjector.license.LifetimeUserManager;
import com.my.ffh4xinjector.R;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * Lifetime Access Panel Controller
 * Manages the UI for lifetime user access verification and activation
 */
public class LifetimeAccessController {
    private static final String TAG = "LifetimeAccessController";
    
    private Context context;
    private View panelView;
    private LifetimeUserManager userManager;
    
    // UI Components
    private TextView accessStatusText;
    private TextView statusTitle;
    private TextView statusDescription;
    private TextView deviceIdText;
    private TextView activationDateText;
    private LinearLayout activationInfoLayout;
    private CardView statusCard;
    private CardView licenseInputCard;
    private TextInputEditText licenseKeyInput;
    private Button activateButton;
    private Button checkStatusButton;
    private Button contactSupportButton;
    
    // Feature status indicators
    private TextView autoaimStatusText;
    private TextView espStatusText;
    private TextView recoilStatusText;
    private TextView speedStatusText;
    
    // Listeners
    private OnAccessChangeListener accessChangeListener;
    
    public interface OnAccessChangeListener {
        void onAccessGranted(int accessType);
        void onAccessDenied();
        void onActivationRequired();
    }
    
    public LifetimeAccessController(Context context) {
        this.context = context;
        this.userManager = LifetimeUserManager.getInstance(context);
        initializePanel();
        setupUserManagerListener();
    }
    
    private void initializePanel() {
        LayoutInflater inflater = LayoutInflater.from(context);
        panelView = inflater.inflate(R.layout.lifetime_access_panel, null);
        
        findViews();
        setupListeners();
        loadCurrentStatus();
        updateUI();
    }
    
    private void findViews() {
        accessStatusText = panelView.findViewById(R.id.text_access_status);
        statusTitle = panelView.findViewById(R.id.text_status_title);
        statusDescription = panelView.findViewById(R.id.text_status_description);
        deviceIdText = panelView.findViewById(R.id.text_device_id);
        activationDateText = panelView.findViewById(R.id.text_activation_date);
        activationInfoLayout = panelView.findViewById(R.id.layout_activation_info);
        statusCard = panelView.findViewById(R.id.card_status);
        licenseInputCard = panelView.findViewById(R.id.card_license_input);
        licenseKeyInput = panelView.findViewById(R.id.edit_license_key);
        activateButton = panelView.findViewById(R.id.btn_activate);
        checkStatusButton = panelView.findViewById(R.id.btn_check_status);
        contactSupportButton = panelView.findViewById(R.id.btn_contact_support);
        
        // Feature status indicators
        autoaimStatusText = panelView.findViewById(R.id.text_autoaim_status);
        espStatusText = panelView.findViewById(R.id.text_esp_status);
        recoilStatusText = panelView.findViewById(R.id.text_recoil_status);
        speedStatusText = panelView.findViewById(R.id.text_speed_status);
    }
    
    private void setupListeners() {
        // License key input formatting
        licenseKeyInput.addTextChangedListener(new TextWatcher() {
            private boolean isFormatting = false;
            
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}
            
            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {}
            
            @Override
            public void afterTextChanged(Editable s) {
                if (isFormatting) return;
                
                isFormatting = true;
                String input = s.toString().replaceAll("[^A-Z0-9]", "");
                
                if (input.length() > 16) {
                    input = input.substring(0, 16);
                }
                
                StringBuilder formatted = new StringBuilder();
                for (int i = 0; i < input.length(); i++) {
                    if (i > 0 && i % 4 == 0) {
                        formatted.append("-");
                    }
                    formatted.append(input.charAt(i));
                }
                
                s.replace(0, s.length(), formatted.toString());
                updateActivateButtonState();
                isFormatting = false;
            }
        });
        
        // Activate button
        activateButton.setOnClickListener(v -> activateLicense());
        
        // Check status button
        checkStatusButton.setOnClickListener(v -> checkAccessStatus());
        
        // Contact support button
        contactSupportButton.setOnClickListener(v -> contactSupport());
    }
    
    private void setupUserManagerListener() {
        userManager.setAccessStatusListener(new LifetimeUserManager.OnAccessStatusChangeListener() {
            @Override
            public void onAccessGranted(int accessType) {
                updateUI();
                if (accessChangeListener != null) {
                    accessChangeListener.onAccessGranted(accessType);
                }
            }
            
            @Override
            public void onAccessDenied(String reason) {
                updateUI();
                showMessage("Access Denied: " + reason, false);
                if (accessChangeListener != null) {
                    accessChangeListener.onAccessDenied();
                }
            }
            
            @Override
            public void onVerificationRequired() {
                showMessage("Verification required. Please check your internet connection.", false);
            }
            
            @Override
            public void onOfflineMode() {
                showMessage("Running in offline mode. Limited time remaining.", false);
            }
        });
    }
    
    private void loadCurrentStatus() {
        // Load device ID
        deviceIdText.setText(userManager.getDeviceId());
        
        // Load activation date if available
        long activationDate = userManager.getActivationDate();
        if (activationDate > 0) {
            SimpleDateFormat sdf = new SimpleDateFormat("MMM dd, yyyy", Locale.getDefault());
            activationDateText.setText(sdf.format(new Date(activationDate)));
            activationInfoLayout.setVisibility(View.VISIBLE);
        } else {
            activationInfoLayout.setVisibility(View.GONE);
        }
        
        // Load license key if available
        String licenseKey = userManager.getLicenseKey();
        if (licenseKey != null) {
            licenseKeyInput.setText(licenseKey);
        }
    }
    
    private void updateUI() {
        int accessStatus = userManager.getAccessStatus();
        
        switch (accessStatus) {
            case LifetimeUserManager.ACCESS_LIFETIME:
                updateUIForLifetimeAccess();
                break;
            case LifetimeUserManager.ACCESS_TRIAL:
                updateUIForTrialAccess();
                break;
            case LifetimeUserManager.ACCESS_EXPIRED:
                updateUIForExpiredAccess();
                break;
            case LifetimeUserManager.ACCESS_BANNED:
                updateUIForBannedAccess();
                break;
            default:
                updateUIForNoAccess();
                break;
        }
        
        updateFeatureStatus(accessStatus);
    }
    
    private void updateUIForLifetimeAccess() {
        accessStatusText.setText("ACTIVE");
        accessStatusText.setTextColor(context.getColor(R.color.status_active));
        
        statusTitle.setText("Lifetime Access Active");
        statusDescription.setText("All premium features are unlocked. Thank you for your support!");
        
        licenseInputCard.setVisibility(View.GONE);
        activationInfoLayout.setVisibility(View.VISIBLE);
        
        statusCard.setCardBackgroundColor(context.getColor(R.color.success_background));
    }
    
    private void updateUIForTrialAccess() {
        accessStatusText.setText("TRIAL");
        accessStatusText.setTextColor(context.getColor(R.color.status_trial));
        
        statusTitle.setText("Trial Access");
        statusDescription.setText("You have limited access. Upgrade to lifetime for full features.");
        
        licenseInputCard.setVisibility(View.VISIBLE);
        statusCard.setCardBackgroundColor(context.getColor(R.color.warning_background));
    }
    
    private void updateUIForExpiredAccess() {
        accessStatusText.setText("EXPIRED");
        accessStatusText.setTextColor(context.getColor(R.color.status_expired));
        
        statusTitle.setText("Access Expired");
        statusDescription.setText("Your access has expired. Please verify your license or contact support.");
        
        licenseInputCard.setVisibility(View.VISIBLE);
        statusCard.setCardBackgroundColor(context.getColor(R.color.error_background));
    }
    
    private void updateUIForBannedAccess() {
        accessStatusText.setText("BANNED");
        accessStatusText.setTextColor(context.getColor(R.color.status_banned));
        
        statusTitle.setText("Access Banned");
        statusDescription.setText("Your license has been banned. Please contact support for assistance.");
        
        licenseInputCard.setVisibility(View.GONE);
        statusCard.setCardBackgroundColor(context.getColor(R.color.error_background));
    }
    
    private void updateUIForNoAccess() {
        accessStatusText.setText("INACTIVE");
        accessStatusText.setTextColor(context.getColor(R.color.status_inactive));
        
        statusTitle.setText("No Access");
        statusDescription.setText("No lifetime access detected. Please enter your license key to activate.");
        
        licenseInputCard.setVisibility(View.VISIBLE);
        activationInfoLayout.setVisibility(View.GONE);
        statusCard.setCardBackgroundColor(context.getColor(R.color.card_background));
    }
    
    private void updateFeatureStatus(int accessStatus) {
        boolean hasAccess = accessStatus == LifetimeUserManager.ACCESS_LIFETIME;
        
        String statusText = hasAccess ? "UNLOCKED" : "LOCKED";
        int statusColor = hasAccess ? R.color.status_unlocked : R.color.status_locked;
        
        autoaimStatusText.setText(statusText);
        autoaimStatusText.setTextColor(context.getColor(statusColor));
        
        espStatusText.setText(statusText);
        espStatusText.setTextColor(context.getColor(statusColor));
        
        recoilStatusText.setText(statusText);
        recoilStatusText.setTextColor(context.getColor(statusColor));
        
        speedStatusText.setText(statusText);
        speedStatusText.setTextColor(context.getColor(statusColor));
    }
    
    private void updateActivateButtonState() {
        String licenseKey = licenseKeyInput.getText().toString().trim();
        boolean isValidFormat = licenseKey.matches("^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$");
        
        activateButton.setEnabled(isValidFormat);
        activateButton.setAlpha(isValidFormat ? 1.0f : 0.5f);
    }
    
    private void activateLicense() {
        String licenseKey = licenseKeyInput.getText().toString().trim();
        
        if (licenseKey.isEmpty()) {
            showMessage("Please enter a license key", false);
            return;
        }
        
        // Show loading state
        activateButton.setEnabled(false);
        activateButton.setText("Activating...");
        
        userManager.activateLifetimeAccess(licenseKey, new LifetimeUserManager.OnActivationListener() {
            @Override
            public void onActivationSuccess(String message) {
                showMessage(message, true);
                updateUI();
                resetActivateButton();
            }
            
            @Override
            public void onActivationFailed(String reason) {
                showMessage("Activation failed: " + reason, false);
                resetActivateButton();
            }
        });
    }
    
    private void resetActivateButton() {
        activateButton.setText("Activate Lifetime Access");
        updateActivateButtonState();
    }
    
    private void checkAccessStatus() {
        checkStatusButton.setEnabled(false);
        checkStatusButton.setText("Checking...");
        
        userManager.checkAccessStatus(new LifetimeUserManager.OnAccessCheckListener() {
            @Override
            public void onAccessResult(int accessStatus, String message) {
                showMessage(message, accessStatus == LifetimeUserManager.ACCESS_LIFETIME);
                updateUI();
                
                checkStatusButton.setText("Check Status");
                checkStatusButton.setEnabled(true);
            }
        });
    }
    
    private void contactSupport() {
        // Implement support contact functionality
        String deviceId = userManager.getDeviceId();
        String licenseKey = userManager.getLicenseKey();
        
        StringBuilder supportInfo = new StringBuilder();
        supportInfo.append("Device ID: ").append(deviceId).append("\n");
        if (licenseKey != null) {
            supportInfo.append("License: ").append(maskLicenseKey(licenseKey)).append("\n");
        }
        supportInfo.append("Status: ").append(getAccessStatusText()).append("\n");
        
        // You can implement email intent or web support here
        showMessage("Support Info:\n" + supportInfo.toString(), true);
    }
    
    private String maskLicenseKey(String licenseKey) {
        if (licenseKey == null || licenseKey.length() < 8) return "****";
        return licenseKey.substring(0, 4) + "-****-****-" + licenseKey.substring(licenseKey.length() - 4);
    }
    
    private String getAccessStatusText() {
        switch (userManager.getAccessStatus()) {
            case LifetimeUserManager.ACCESS_LIFETIME: return "Lifetime Active";
            case LifetimeUserManager.ACCESS_TRIAL: return "Trial";
            case LifetimeUserManager.ACCESS_EXPIRED: return "Expired";
            case LifetimeUserManager.ACCESS_BANNED: return "Banned";
            default: return "No Access";
        }
    }
    
    private void showMessage(String message, boolean isSuccess) {
        Toast.makeText(context, message, Toast.LENGTH_LONG).show();
    }
    
    // Public methods
    public View getPanelView() {
        return panelView;
    }
    
    public void setOnAccessChangeListener(OnAccessChangeListener listener) {
        this.accessChangeListener = listener;
    }
    
    public void refreshPanel() {
        loadCurrentStatus();
        updateUI();
    }
    
    public boolean hasLifetimeAccess() {
        return userManager.hasLifetimeAccess();
    }
    
    public void performAccessCheck() {
        checkAccessStatus();
    }
}
